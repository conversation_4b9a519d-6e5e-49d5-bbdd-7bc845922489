// Responsive improvements for cash register settings
@media only screen and (max-width: 768px) {
    .container-fluid {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    // Stack buttons vertically on mobile
    .col-6 {
        margin-bottom: 15px;
    }

    // Better spacing for form elements
    .col-md-12 {
        margin-bottom: 20px;
    }
}

@media only screen and (max-width: 576px) {
    .container-fluid {
        padding-left: 10px !important;
        padding-right: 10px !important;
        padding-top: 15px !important;
        padding-bottom: 15px !important;
    }

    // Stack buttons vertically with full width
    .col-6 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 10px;
    }

    // Better spacing for form elements
    .col-md-12 {
        margin-bottom: 15px;
    }

    // Improve form field spacing
    ::ng-deep {
        .mat-form-field {
            width: 100%;
            margin-bottom: 10px;
        }

        .mat-form-field-wrapper {
            padding-bottom: 1em;
        }
    }
}