#settingsContainer {
  background-color: white;
  padding: 30px 45px;
  color: #022b3a;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
}

#footer {
  margin-top: 50px;
}

#title {
  color: #022b3a;
  margin-top: 15px;

  h4 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 20px;
  }
}

// Enhanced responsive breakpoints
@media only screen and (max-width: 992px) {
  #settingsContainer {
    padding: 25px 35px;
  }

  .col-md-8 {
    flex: 0 0 90%;
    max-width: 90%;
  }

  #footer {
    margin-top: 40px;

    .col-md-6 {
      flex: 0 0 80%;
      max-width: 80%;
    }
  }
}

@media only screen and (max-width: 768px) {
  #settingsContainer {
    padding: 20px 25px;
  }

  .col-md-8 {
    flex: 0 0 95%;
    max-width: 95%;
  }

  #title {
    h4 {
      font-size: 20px;
      text-align: center;
      margin-bottom: 25px;
    }
  }

  #footer {
    margin-top: 35px;

    .col-md-6 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
}

@media only screen and (max-width: 576px) {
  #settingsContainer {
    padding: 15px 15px;
  }

  .col-md-8 {
    flex: 0 0 100%;
    max-width: 100%;
  }

  #title {
    margin-top: 10px;

    h4 {
      font-size: 18px;
      text-align: center;
      margin-bottom: 20px;
    }
  }

  #footer {
    margin-top: 30px;

    .col-md-6 {
      flex: 0 0 100%;
      max-width: 100%;
    }
  }
}

@media only screen and (max-width: 480px) {
  #settingsContainer {
    padding: 10px 10px;
    border-radius: 0;
  }

  #title {
    h4 {
      font-size: 16px;
      margin-bottom: 15px;
    }
  }

  #footer {
    margin-top: 25px;
  }
}
