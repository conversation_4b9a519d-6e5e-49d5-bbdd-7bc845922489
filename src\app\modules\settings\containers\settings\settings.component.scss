p {
    margin-bottom: 5px;
    color: #022B3A;
}

.row {
    margin-bottom: 15px;
}

a {
    text-decoration: none;
    cursor: pointer;
}

// Responsive improvements
@media only screen and (max-width: 768px) {
    .container-fluid {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    .row {
        margin-bottom: 20px;
    }

    // Stack email delete buttons on mobile
    .col-md-11, .col-md-1 {
        margin-bottom: 10px;
    }
}

@media only screen and (max-width: 576px) {
    .container-fluid {
        padding-left: 10px !important;
        padding-right: 10px !important;
        padding-top: 15px !important;
        padding-bottom: 15px !important;
    }

    p {
        font-size: 14px;
        line-height: 1.4;
    }

    // Better spacing for mobile
    .row {
        margin-bottom: 15px;
    }

    // Make add email link more touch-friendly
    a {
        padding: 10px 0;
        font-size: 16px;
    }
}