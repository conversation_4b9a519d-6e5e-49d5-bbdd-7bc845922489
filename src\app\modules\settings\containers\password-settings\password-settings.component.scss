// Password settings styles

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
    color: #333;
}

.input-group {
    margin-bottom: 10px;
}

.form-control {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    font-size: 14px;

    &:focus {
        border-color: #2cafc9;
        box-shadow: 0 0 0 0.2rem rgba(44, 175, 201, 0.25);
    }
}

.btn-outline-secondary {
    border-color: #ddd;
    color: #666;

    &:hover {
        background-color: #f8f9fa;
        border-color: #adb5bd;
    }
}

.text-danger {
    margin-top: 5px;

    small {
        font-size: 12px;
    }
}

.alert {
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 15px;
}

// Responsive improvements
@media only screen and (max-width: 768px) {
    .container-fluid {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    .col-md-12 {
        margin-bottom: 20px;
    }

    .form-label {
        font-size: 14px;
    }

    .form-control {
        font-size: 16px; // Prevent zoom on iOS
        padding: 14px 12px;
    }

    .btn-outline-secondary {
        padding: 14px 16px;
    }
}

@media only screen and (max-width: 576px) {
    .container-fluid {
        padding-left: 10px !important;
        padding-right: 10px !important;
        padding-top: 15px !important;
        padding-bottom: 15px !important;
    }

    .col-md-12 {
        margin-bottom: 15px;
    }

    .form-label {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .form-control {
        font-size: 16px; // Prevent zoom on iOS
        padding: 12px 10px;
    }

    .btn-outline-secondary {
        padding: 12px 14px;
        min-width: 50px;
    }

    .text-danger small {
        font-size: 11px;
    }

    .alert {
        padding: 10px;
        font-size: 13px;
    }

    // Better button spacing
    .col-12 {
        margin-top: 20px;
    }
}
