<div class="container" *transloco="let t">
  <div class="row align-items-center">
    <div class="col-lg-4">
      <h5>{{ t("receipts.items") }}:</h5>
    </div>
    <div class="col-lg-3"></div>
    <div class="col-lg-5">
      <app-fiscomm-select
        placeholder="{{ t('input.tax_type') }}"
        [options]="taxRateLabels"
        [control]="mainTaxLabel"
      >
      </app-fiscomm-select>
    </div>
  </div>
  <div class="row">
    <div class="col-lg-12">
      <table class="table">
        <tr class="titleRow">
          <th>{{ t("receipts.item_name") }}</th>
          <th>GTIN</th>
          <th>{{ t("items.amount") }}</th>
          <th>{{ t("price") }}</th>
          <th>{{ t("discount") }}</th>
          <th>{{ t("tax") }}</th>
          <th>{{ t("total") }}</th>
          <th class="icon-col"></th>
        </tr>
        <tr
          *ngFor="
            let itemRow of formService.getItemsFormArray().controls;
            let i = index
          "
        >
          <td *ngIf="products && filteredOptions">
            <app-fiscomm-input
              appearance="outline"
              type="text"
              placeholder="Naziv"
              [control]="formService.getItemControl(i, 'name')"
              [autoComplete]="auto"
            >
            </app-fiscomm-input>
            <mat-autocomplete
              autoActiveFirstOption
              panelWidth="fit-content"
              #auto="matAutocomplete"
              (optionSelected)="onNameSelected($event, i)"
            >
              <mat-option
                *ngFor="let option of filteredOptions[i]; let optionIndex = index"
                [value]="getProductIdentifier(option, optionIndex, i)"
              >
                <div class="product-option">
                  <span class="product-name">{{ option.name }}</span>
                  <span class="product-details" *ngIf="option.gtin || option.productCode">
                    <small class="text-muted">
                      <span *ngIf="option.gtin">(GTIN: {{ option.gtin }})</span>
                      <span *ngIf="option.productCode && option.gtin"> | </span>
                      <span *ngIf="option.productCode">(Code: {{ option.productCode }})</span>
                    </small>
                  </span>
                  <span class="product-price"
                    *ngIf="getProductPriceForDisplay(option) !== '0.00'"
                  >
                    ({{ getProductPriceForDisplay(option) }}
                    {{ formService.getCurrencyControl().value }})
                  </span>
                </div>
              </mat-option>
            </mat-autocomplete>
          </td>
          <td>
            <app-fiscomm-input
              [control]="formService.getItemControl(i, 'gtin')"
              placeholder="GTIN"
            >
            </app-fiscomm-input>
          </td>
          <td>
            <app-fiscomm-input
              [control]="formService.getItemControl(i, 'quantity')"
              [decimals]="2"
              placeholder="{{ t('items.amount') }}"
              type="number"
            >
            </app-fiscomm-input>
          </td>
          <td>
            <app-fiscomm-input
              [control]="formService.getItemControl(i, 'unitPrice')"
              [decimals]="2"
              placeholder="{{ t('price') }}"
              type="number"
              [suffix]="formService.getCurrencyControl().value"
            >
            </app-fiscomm-input>
          </td>
          <td>
            <app-fiscomm-input
              [control]="formService.getItemControl(i, 'discount')"
              [decimals]="2"
              placeholder="{{ t('discount') }}"
              type="number"
              suffix="%"
            >
            </app-fiscomm-input>
          </td>
          <td>
            <app-fiscomm-select
              [control]="formService.getItemControl(i, 'label')"
              placeholder="{{ t('tax_type') }}"
              [options]="taxRateLabels"
            >
            </app-fiscomm-select>
          </td>
          <td>
            {{ formService.getItemControl(i, "totalAmount").value }} {{ formService.getCurrencyControl().value }}
            <div *ngIf="formService.getCurrencyControl().value !== 'RSD'" class="rsd-conversion">
              ({{ getItemTotalInRSD(i) | number:'1.2-2' }} RSD)
            </div>
          </td>
          <td class="icon-col">
            <mat-icon (click)="formService.removeItemRow(i)">delete</mat-icon>
          </td>
        </tr>
      </table>
    </div>
    <div class="col-lg-12 mb-2 button">
      <span class="d-flex align-items-center" (click)="addItem()"
        ><mat-icon>add</mat-icon>{{ t("items.add") }}</span
      >
    </div>
    <div class="col-lg-12 mt-1 pt-3 d-flex justify-content-between footer">
      <p>{{ t("total_sum") }}:</p>
      <p>
        {{ formService.getItemsAmountSum() }} {{ formService.getCurrencyControl().value }}
        <span *ngIf="formService.getCurrencyControl().value !== 'RSD'" class="rsd-conversion">
          ({{ getTotalItemsInRSD() | number:'1.2-2' }} RSD)
        </span>
      </p>
    </div>
  </div>
</div>
