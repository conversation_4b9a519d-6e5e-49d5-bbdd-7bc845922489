<div class="container-fluid" *transloco="let t">
    <div class="gap-3 products-header">
        <div><app-success-btn text="{{t('items.add')}}" (click)="actionEmitter.emit({action:'add'})"></app-success-btn></div>
        <div>
            <input type="file" #fileInput (change)="onFileSelected($event)" accept=".xlsx,.xls,.tsv,.csv" style="display: none">
            <app-success-btn text="{{t('items.import')}}" (click)="fileInput.click()"></app-success-btn>
        </div>
        <div class="w-full">
            <button mat-raised-button color="primary" (click)="showImportTemplate()" class="w-full">
              <mat-icon>help_outline</mat-icon>
              {{ t('items.import_template_title') }}
            </button>
        </div>
    </div>
</div>
