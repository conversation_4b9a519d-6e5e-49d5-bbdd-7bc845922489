<div
   class="container-fluid ps-lg-5 pe-lg-5 pt-lg-4 pb-lg-4 ps-md-3 pe-lmd-3 pt-md-2 pb-md-2"
  *transloco="let t"
>
  <app-page-title text="{{ t('settings') }}"></app-page-title>
  <app-settings-navbar></app-settings-navbar>
  <div class="container-fluid">
    <h3>{{ t("settings_page.receipt_customization") }}</h3>
    <div class="row">
      <div class="col-lg-7 pe-0 lg-pe-5">
        <div class="row">
          <div class="col-md-3 d-flex">
            <p>{{ t("settings_page.receipt_width") }}"</p>
          </div>
          <div class="col-md-9 d-flex">
            <p>50mm</p>
            <span></span>
            <mat-slider
              [(value)]="receiptsInfo.width"
              color="primary"
              min="50"
              max="144"
              step="1"
              discrete
              [displayWith]="formatLabel"
            >
              <input matSliderThumb />
            </mat-slider>
            <p>144mm</p>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <p>{{ t("settings_page.receipt_text") }}</p>
          </div>
          <div class="col-md-12">
            <textarea
              (keyup)="underReceiptChanged($event)"
              (change)="underReceiptChanged($event)"
              [value]="receiptsInfo.underReceipt"
              placeholder="{{ t('settings_page.input.receipt_text') }}"
            ></textarea>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12">
            <mat-slide-toggle
              [checked]="receiptsInfo.orderNumUnderReceipt"
              (change)="toggleOrderNumUnderReceipt()"
              color="primary"
              >{{
                t("settings_page.input.add_receipt_order")
              }}"</mat-slide-toggle
            >
          </div>
          <!-- <div class="col-md-12">
            <mat-slide-toggle
              [checked]="clientData?.receipts?.isEmailActive"
              (change)="toggleEmail()"
              color="primary"
              >Uključi slanje emailova kupcima</mat-slide-toggle
            >
          </div> -->
        </div>
        <!-- <div class="row">
          <p>Slika ispod racuna</p>
          <div class="col-md-6">
            <app-fiscomm-input
              (imageChange)="onImageChange($event)"
              type="image"
              [control]="imageControl"
            ></app-fiscomm-input>
          </div>
          <div class="col-md-2"></div>
          <div class="col-md-4 mt-2">
            <button (click)="resetImageInput()">Ukloni sliku</button>
          </div>
        </div> -->
      </div>
      <div class="col-md-5">
        <p>Izgled racuna</p>
        <div class=" receipt-container">
          <app-receipt-preview
              [isReceiptNumberEnabled]="receiptsInfo.orderNumUnderReceipt"
              [width]="receiptsInfo.width + 'mm'"
              [additonalText]="receiptsInfo.underReceipt"
              [urlPreview]="urlPreview"
            ></app-receipt-preview>
        </div>
    
      </div>
    </div>
    <div class="row d-flex justify-content-center">
      <div class="col-md-6">
        <app-success-btn
          text="{{ t('settings_page.save_changes') }}"
          (click)="saveReceiptsSettings()"
        ></app-success-btn>
      </div>
    </div>
  </div>
</div>
