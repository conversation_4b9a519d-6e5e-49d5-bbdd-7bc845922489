<div
  class="container-fluid ps-lg-5 pe-lg-5 pt-lg-4 pb-lg-4 ps-md-3 pe-lmd-3 pt-md-2 pb-md-2"
  *transloco="let t"
>
  <app-page-title text="{{ t('settings') }}"></app-page-title>
  <app-settings-navbar></app-settings-navbar>
  <app-settings-container title="Podešavanja kase">
    <ng-container content>
      <div class="row">
        <form [formGroup]="form">
          <div class="col-md-12">
            <app-fiscomm-select
              [control]="getControl('invoiceType')"
              placeholder="{{ t('receipts.input.receipt_type') }}"
              [options]="selectDataService.getInvoiceTypes()"
              [enableNone]="true"
            >
            </app-fiscomm-select>
          </div>
          <div class="col-md-12">
            <app-fiscomm-select
              [control]="getControl('transactionType')"
              placeholder="{{ t('receipts.input.transaction_type') }}"
              [options]="
                selectDataService.getTransactionTypes(
                  getControl('invoiceType').value
                )
              "
              [enableNone]="true"
            >
            </app-fiscomm-select>
          </div>
          <div class="col-md-12">
            <app-fiscomm-select
              placeholder="{{ t('input.tax_type') }}"
              [options]="taxRateLabels"
              [control]="getControl('taxLabel')"
              [enableNone]="true"
            >
            </app-fiscomm-select>
          </div>
          <div class="col-md-12">
            <app-fiscomm-select
              placeholder="{{ t('settings_page.default_payment_method') || 'Default payment method' }}"
              [options]="selectDataService.getPaymentTypes()"
              [control]="getControl('defaultPaymentMethod')"
              [enableNone]="true"
            >
            </app-fiscomm-select>
          </div>
          <div class="col-md-12">
            <app-cashier-autocomplete
              [placeholder]="'Podrazumevani kasir'"
              [control]="getControl('defaultCashier')">
            </app-cashier-autocomplete>
          </div>
          <div class="col-md-12">
            <app-fiscomm-input
              placeholder="{{ 'Podrazumevani footer na računu' }}"
              [control]="getControl('receiptFooter')"
              [appearance]="'outline'"
            >
            </app-fiscomm-input>
          </div>
        </form>
      </div>
    </ng-container>
    <ng-container buttons>
      <div class="row">
        <div class="col-6">
          <app-success-btn
           style="height: 100%;"
            text="{{ t('settings_page.save_changes') }}"
            (click)="saveSettings()"
          ></app-success-btn>
        </div>
        <div class="col-6">
          <app-primary-btn
            text="{{ t('settings_page.reset') }}"
            (click)="resetSettings()"
          ></app-primary-btn>
        </div>
      </div>
    </ng-container>
  </app-settings-container>
</div>
