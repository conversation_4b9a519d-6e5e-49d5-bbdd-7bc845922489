<div class="container-fluid ps-lg-5 pe-lg-5 pt-lg-4 pb-lg-4 ps-md-3 pe-lmd-3 pt-md-2 pb-md-2" *transloco="let t">
  <app-page-title text="{{ t('dashboard.reports') }}"></app-page-title>
  <div *ngIf="reportUrl" class="alert alert-primary alert-dismissible fade show" role="alert">
    <button type="button" class="btn-close" (click)="closeReportUrl()" data-bs-dismiss="alert"
      aria-label="Close"></button>
    <strong>{{ t("reports.report") }} "{{ reportName }}"
      {{ t("reports.generated") }}.
    </strong>
    <p>
      {{ reportDates.from | date : "dd.M.yyyy" }} -
      {{ reportDates.to | date : "dd.M.yyyy" }}
    </p>
    <a [href]="reportUrl">{{ t("reports.download") }}...</a>
  </div>
  <div class="container-fluid filters">
    <a id="settings" routerLink="/podesavanja/izvestaji" target="_blank"><mat-icon>settings</mat-icon></a>
    <div class="row">
      <div class="col-md-12">
        <p>{{ t("required_info") }}</p>
        <p>{{ t("generation_interval_info") }}</p>
      </div>
    </div>
    <div class="row">
      <div class="col-md-6">
        <app-fiscomm-select [control]="getControl('type')" placeholder="{{ t('reports.report_type') }}"
          [options]="reportTypes"></app-fiscomm-select>
      </div>
      <div class="col-md-6">
        <app-fiscomm-input [control]="getControl('name')"
          placeholder="{{ t('reports.report_name') }}"></app-fiscomm-input>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12">
        <mat-form-field appearance="outline" class="date">
          <mat-label>{{ t("input.date_from_to") }}:</mat-label>
          <mat-date-range-input [rangePicker]="picker" [max]="maxDate">
            <input matStartDate [formControl]="getControl('dateFrom')" />
            <input matEndDate [formControl]="getControl('dateTo')" />
          </mat-date-range-input>
          <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-date-range-picker #picker></mat-date-range-picker>
        </mat-form-field>
      </div>
    </div>
    <div class="row">
      <div class="md-12 d-flex justify-content-center">
        <button [disabled]="!reportForm.valid || cooldownTimer" (click)="createReport()">
          {{ t("reports.add_new") }}

          <span *ngIf="cooldownTimer> 0" class="cooldown">
          ({{ cooldownTimer | timer }})
          </span>
        </button>
      </div>
    </div>
  </div>

  <div id="tableHead">
    <div class="col-md-12">
      <h4>Pregled ranijih izveštaja</h4>
    </div>
  </div>

  <div class="table-container">
  <table>
      <thead>
        <th>Tip</th>
        <th>Naziv</th>
        <th>Datum od</th>
        <th>Datum do</th>
        <th>Vreme kreiranja</th>
        <th>Akcija</th>
      </thead>
      <tbody>
        <tr *ngFor="let report of reports">
          <td>{{t(report.transloco)}}</td>
          <td>{{report.name}}</td>
          <td>{{report.from | date : "dd.M.yyyy"}}</td>
          <td>{{report.to | date : "dd.M.yyyy"}}</td>
          <td>{{report.createdAt | date : "dd.M.yyyy HH:mm"}}</td>
          <td><a [href]="report.url">Preuzmi</a></td>
        </tr>
      </tbody>
    </table>
  </div>
  
</div>
