<!-- =============================================================================
     DASHBOARD SIDENAV - COMPLETE REWORK
     ============================================================================= -->

<mat-sidenav-container fullscreen *transloco="let t" id="dashboard">
  <!-- SIDENAV -->
  <mat-sidenav #sidenav
               [mode]="isMobile ? 'over' : 'side'"
               class="fiscomm-sidenav"
               (closed)="onSidenavClose()"
               [class.expanded]=" sidenavWidth > 9"
               [class.collapsed]=" sidenavWidth <= 8"
               [opened]="sideNavOpened">

    <!-- LOGO SECTION -->
    <div class="nav-logo" (click)="openFiscommLanding()">
      <div class="logo-icon">
        <img src="assets/fiscomm-e-light.png" alt="Fiscomm" />
      </div>
      <div class="logo-text">
        <img src="assets/fiscomm-light.png" alt="Fiscomm" />
      </div>
    </div>
    

    

    <!-- MAIN NAVIGATION SECTION -->
    <div class="nav-section">

      <!-- Home -->
      <a class="nav-item"
         routerLink="pocetna"
         routerLinkActive="active"
         (click)="toggleSidenav('navigation')">
        <mat-icon class="nav-icon">home</mat-icon>
        <div class="nav-text">
          <h5>{{ t("dashboard.home") }}</h5>
        </div>
      </a>

      <!-- Receipts Section Divider -->
      <div class="nav-divider">
        <div class="divider-text">{{ t("dashboard.receipts") }}</div>
      </div>

      <!-- Receipts Page -->
      <a class="nav-item"
         routerLink="pregled-racuna"
         routerLinkActive="active"
         (click)="toggleSidenav('navigation')">
        <mat-icon class="nav-icon">receipt</mat-icon>
        <div class="nav-text">
          <h5>{{ t("dashboard.receipts_page") }}</h5>
        </div>
      </a>

      <!-- Reports -->
      <a class="nav-item"
         routerLink="izvestaji"
         routerLinkActive="active"
         (click)="toggleSidenav('navigation')">
        <mat-icon class="nav-icon">bar_chart</mat-icon>
        <div class="nav-text">
          <h5>{{ t("dashboard.reports") }}</h5>
        </div>
      </a>

      <!-- Products -->
      <a class="nav-item"
         routerLink="proizvodi"
         routerLinkActive="active"
         (click)="toggleSidenav('navigation')">
        <mat-icon class="nav-icon">inventory_2</mat-icon>
        <div class="nav-text">
          <h5>{{ t("dashboard.products") }}</h5>
        </div>
      </a>

      <!-- Cashiers -->
      <a class="nav-item"
         routerLink="kasiri"
         routerLinkActive="active"
         (click)="toggleSidenav('navigation')">
        <mat-icon class="nav-icon">people</mat-icon>
        <div class="nav-text">
          <h5>{{ t("dashboard.cashiers") }}</h5>
        </div>
      </a>

      <!-- Custom Fields -->
      <a class="nav-item"
         routerLink="proizvoljna-polja"
         routerLinkActive="active"
         (click)="toggleSidenav('navigation')">
        <mat-icon class="nav-icon">view_list</mat-icon>
        <div class="nav-text">
          <h5>{{ t("dashboard.custom_fields") }}</h5>
        </div>
      </a>

      <!-- NEXT_VERSION -->
      <!--
      <div
        [class]="
          sidenavWidth < 9
            ? 'd-flex justify-content-center divider '
            : 'divider '
        "
      >
        <small *ngIf="sidenavWidth > 9" class="text-center w-100"
          >Fakture</small
        >
      </div>
      <mat-list-item
        [class]="
          sidenavWidth < 9 ? 'd-flex flex-column align-items-center ' : ''
        "
        routerLinkActive="active"
        (click)="toggleSidenav('navigation')"
        routerLink="e-fakture"
      >
        <mat-icon>description</mat-icon>
        <div fxFlex="10"></div>
        <div *ngIf="sidenavWidth > 9" class="sidenav-item">
          <h5>E-fakture</h5>
        </div>
      </mat-list-item> -->

      <!-- Integrations Section -->
      <ng-container *ngIf="currentUser?.integrationType">
        <div class="nav-divider">
          <div class="divider-text">Integracije</div>
        </div>

        <a *ngIf="currentUser.integrationType == 'merchant-pro'"
           class="nav-item"
           routerLink="merchant-pro"
           routerLinkActive="active"
           (click)="toggleSidenav('navigation')">
          <mat-icon class="nav-icon">store</mat-icon>
          <div class="nav-text">
            <h5>Merchant Pro</h5>
          </div>
        </a>
      </ng-container>

      <!-- Support Section Divider -->
      <div class="nav-divider">
        <div class="divider-text">Podrška</div>
      </div>

      <!-- Settings -->
      <a class="nav-item"
         routerLink="podesavanja"
         routerLinkActive="active"
         (click)="toggleSidenav('navigation')">
        <mat-icon class="nav-icon">settings</mat-icon>
        <div class="nav-text">
          <h5>{{ t("dashboard.settings") }}</h5>
        </div>
      </a>

    </div>

          <mat-list-item class="d-flex justify-content-center" *ngIf="sidenavWidth < 9" (click)="toggleSidenav()">
        <mat-icon class="fiscomm-teal-color">chevron_right</mat-icon>
      </mat-list-item>
      <mat-list-item class="d-flex justify-content-center" *ngIf="sidenavWidth > 9" (click)="toggleSidenav()">
        <mat-icon>chevron_left</mat-icon>
      </mat-list-item>

    <!-- BOTTOM NAVIGATION SECTION -->
    <div class="nav-bottom">
      <!-- Create Receipt -->
      <div class="nav-item" (click)="openCreateReceiptDialog()">
        <mat-icon class="nav-icon">add_circle</mat-icon>
        <div class="nav-text">
          <h5>{{ t("dashboard.create_receipt") }}</h5>
        </div>
      </div>

      <!-- Language Selector -->
      <div class="nav-item">
        <mat-icon class="nav-icon" [matMenuTriggerFor]="menu">translate</mat-icon>
        <mat-menu #menu="matMenu">
          <button mat-menu-item
                  *ngFor="let lang of languages"
                  [class]="lang.value == activeLang ? 'activeMenu' : ''"
                  (click)="setLanguageMenu(lang.value)">
            {{ lang.label }}
          </button>
        </mat-menu>
        <div class="nav-text" id="langSelect">
          <app-fiscomm-select [control]="languageControl" [options]="languages"></app-fiscomm-select>
        </div>
      </div>

 

      <!-- Logout -->
      <div class="nav-item" (click)="signOut()">
        <mat-icon class="nav-icon">logout</mat-icon>
        <div class="nav-text">
          <h5>{{ t("dashboard.log_out") }}</h5>
        </div>
      </div>

     

      <!-- Toggle Button (Desktop) -->
      <!-- <div class="nav-item" (click)="toggleSidenav()" *ngIf="!isMobile">
        <mat-icon class="nav-icon fiscomm-teal-color" *ngIf="sidenavWidth <= 9">chevron_right</mat-icon>
        <mat-icon class="nav-icon" *ngIf="sidenavWidth > 9">chevron_left</mat-icon>
      </div> -->
    </div>
  </mat-sidenav>

  <!-- CONTENT AREA -->
  <mat-sidenav-content class="main-content"
                       [ngStyle]="isMobile ? {} : { 'margin-left.em': sidenavWidth }">

    <!-- Mobile Navigation Bar -->
    <div class="mobile-nav" *ngIf="isMobile">
      <button class="mobile-menu-btn" (click)="toggleNavigation()">
        <mat-icon class="fiscomm-teal-color">menu</mat-icon>
      </button>
    </div>


   

    <!-- Main Content -->
    <div class="content-wrapper">
      <router-outlet></router-outlet>
    </div>
  </mat-sidenav-content>
</mat-sidenav-container>
