.mat-slide-toggle {
    padding-top: 10px;
    font-size: 18px;
}

h4 {
    margin-top: 15px;
}

a {
    text-decoration: none;
    cursor: pointer;
}

// Responsive improvements
@media only screen and (max-width: 768px) {
    .container-fluid {
        padding-left: 15px !important;
        padding-right: 15px !important;
    }

    .mat-slide-toggle {
        font-size: 16px;
        padding-top: 15px;
        padding-bottom: 5px;
    }

    h4 {
        margin-top: 20px;
        margin-bottom: 15px;
        font-size: 18px;
    }

    // Stack email delete buttons on mobile
    .col-md-11, .col-md-1 {
        margin-bottom: 10px;
    }
}

@media only screen and (max-width: 576px) {
    .container-fluid {
        padding-left: 10px !important;
        padding-right: 10px !important;
        padding-top: 15px !important;
        padding-bottom: 15px !important;
    }

    .mat-slide-toggle {
        font-size: 14px;
        padding-top: 12px;
        padding-bottom: 8px;
    }

    h4 {
        font-size: 16px;
        margin-top: 15px;
        margin-bottom: 10px;
    }

    // Make add email link more touch-friendly
    a {
        padding: 10px 0;
        font-size: 16px;
    }

    p {
        font-size: 14px;
        line-height: 1.4;
    }
}