import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProductsComponent } from './containers/products/products.component';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from 'src/app/shared/shared.module';
import { ProductsTableComponent } from './components/products-table/products-table.component';
import { ProductsHeaderComponent } from './components/products-header/products-header.component';
import { CreateProductDialogComponent } from './components/create-product-dialog/create-product-dialog.component';
import { UpdateProductDialogComponent } from './components/update-product-dialog/update-product-dialog.component';
import { MatDialogModule } from '@angular/material/dialog';
import { MatTableModule } from '@angular/material/table';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatButtonModule } from '@angular/material/button';
import { MatSortModule } from '@angular/material/sort';
import { RsdCurrencyPipe } from 'src/app/shared/pipes/rsd-currency.pipe';
import { DuplicateProductsDialogComponent } from './components/duplicate-products-dialog/duplicate-products-dialog.component';
import { TranslocoRootModule } from '../../transloco-root.module';
import { ImportTemplateDialogComponent } from './components/import-template-dialog/import-template-dialog.component';

const routes: Routes = [
  {
    path:'',
    component: ProductsComponent
  },
];

@NgModule({
  declarations: [
    ProductsComponent,
    ProductsTableComponent,
    ProductsHeaderComponent,
    CreateProductDialogComponent,
    UpdateProductDialogComponent,
    DuplicateProductsDialogComponent,
    ImportTemplateDialogComponent
  ],
  imports: [
    CommonModule,
    TranslocoRootModule,
    MatDialogModule,
    MatTableModule,
    MatCheckboxModule,
    MatButtonModule,
    MatSortModule,
    SharedModule,
    RouterModule.forChild(routes),
  ],
  providers: [
    RsdCurrencyPipe
  ]
})
export class ProductsModule { }
