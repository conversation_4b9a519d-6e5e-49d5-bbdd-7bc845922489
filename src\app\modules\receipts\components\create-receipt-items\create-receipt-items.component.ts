import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ReceiptsService } from 'src/app/shared/services/backend/receipts/receipts.service';
import { SelectTypesDataService } from 'src/app/shared/services/select-types-data.service';
import { FormControl } from '@angular/forms';
import { FormCreateReceiptService } from '../../services/form-create-receipt.service';
import { TaxService } from 'src/app/shared/services/backend/system/tax.service';
import { ProductsService } from 'src/app/shared/services/backend/products/products.service';
import {
  Subject,
  debounceTime,
  distinctUntilChanged,
  map,
  startWith,
  take,
  takeUntil,
} from 'rxjs';
import { initializeApp } from 'firebase/app';
import { MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';

@Component({
  selector: 'app-create-receipt-items',
  templateUrl: './create-receipt-items.component.html',
  styleUrls: ['./create-receipt-items.component.scss'],
})
export class CreateReceiptItemsComponent implements OnInit {
  taxRateLabels: any[] = [];
  taxRateLabels2: any[] = [];
  mainTaxLabel: FormControl = this.formService.getMainTax();
  products: any;
  filteredOptions: any = [];
  private destroy$ = new Subject<void>();
  private currentCurrency: string = 'RSD';

  constructor(
    public selectDataService: SelectTypesDataService,
    public formService: FormCreateReceiptService,
    private taxService: TaxService,
    private produtService: ProductsService
  ) {
    this.taxService.getTaxRates$().subscribe((response) => {
      this.taxRateLabels = response.map((el: any) => ({
        value: el.value.label,
        label: el.label,
      }));
    });
  }

  ngOnInit(): void {
    this.getProducts();
    setTimeout(() => {
      this.initalizeAutocompleteOptions();
    }, 2000);

    // Subscribe to currency changes to update product prices when currency changes
    this.formService.getCurrencyControl().valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe((newCurrency) => {
        const oldCurrency = this.currentCurrency;
        this.currentCurrency = newCurrency;

        if (oldCurrency !== newCurrency) {
          this.updateAllProductPrices(newCurrency);
        }
      });
  }

  initalizeAutocompleteOptions() {
    this.formService.getItemsFormArray().controls.forEach((controlName, i) => {
      const control = this.formService.getItemControl(i, 'name');
      this.getFiltered(control)
        .pipe(takeUntil(this.destroy$))
        .subscribe((options) => {
          this.filteredOptions[i] = options;
        });
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onNameSelected(event: MatAutocompleteSelectedEvent, index: number) {
    const selectedName = event.option.value;

    console.log(event.option)

    const selectedItem = this.filteredOptions[index].find(
      (option: any) => option.name === selectedName
    );

    if (selectedItem) {
      // Get the selected currency
      const selectedCurrency = this.formService.getCurrencyControl().value;

      // Set price based on currency selection (default to 0 if price not found in that currency)
      let price = 0;

      if (selectedItem.prices && selectedItem.prices[selectedCurrency]) {
        price = selectedItem.prices[selectedCurrency];
      } else if (selectedCurrency === 'RSD' && selectedItem.price) {
        // Fallback to the default price field for RSD
        price = selectedItem.price;
      }

      console.log(price)
      this.formService.getItemControl(index, 'unitPrice').setValue(price);
      this.formService.getItemControl(index, 'quantity').setValue(1);
      this.formService.getItemControl(index, 'gtin').setValue(selectedItem.gtin || '');

      // Set the tax label if it exists on the product
      if (selectedItem.taxLabel) {
        this.formService.getItemControl(index, 'label').setValue(selectedItem.taxLabel.label);
      } else if (this.mainTaxLabel.value) {
        // Fallback to the main tax label if set
        this.formService.getItemControl(index, 'label').setValue(this.mainTaxLabel.value);
      }
    }
  }

  // Method to update all product prices when currency changes
  private updateAllProductPrices(newCurrency: string) {
    const itemControls = this.formService.getItemsFormArray().controls;

    if (itemControls.length === 0) return;

    itemControls.forEach((control, index) => {
      const productName = control.get('name')?.value;

      if (!productName) return;

      const product = this.products.find((p: any) => p.name === productName);

      if (product) {
        let newPrice = 0;

        if (product.prices && product.prices[newCurrency]) {
          newPrice = product.prices[newCurrency];
        } else if (newCurrency === 'RSD' && product.price) {
          // Fallback to the default price field for RSD
          newPrice = product.price;
        }

        this.formService.getItemControl(index, 'unitPrice').setValue(newPrice);
      }
    });
  }

  // Helper methods for RSD conversion
  getItemPriceInRSD(index: number): number {
    const unitPrice = this.formService.getItemControl(index, 'unitPrice').value || 0;
    const exchangeRate = this.formService.getExchangeRateControl().value || 1;
    return unitPrice * exchangeRate;
  }

  getItemTotalInRSD(index: number): number {
    const totalAmount = this.formService.getItemControl(index, 'totalAmount').value || 0;
    const exchangeRate = this.formService.getExchangeRateControl().value || 1;
    return totalAmount * exchangeRate;
  }

  getTotalItemsInRSD(): number {
    const totalItems = this.formService.getItemsAmountSum() || 0;
    const exchangeRate = this.formService.getExchangeRateControl().value || 1;
    return totalItems * exchangeRate;
  }

  public getFiltered(control: FormControl) {
    return control.valueChanges.pipe(
      startWith(''),
      debounceTime(300),
      map((value) => {
        return this._filter(value || '');
      })
    );
  }

  private _filter(value: string) {
    const filterValue = value.toLowerCase();

    let tmp = this.products.filter((product: any) =>
      product.name.toLowerCase().includes(filterValue) ||
      (product.productCode && product.productCode.toLowerCase().includes(filterValue))
    );
    return tmp;
  }

  getProducts() {
    this.produtService.getAllProducts().subscribe((response) => {
      this.products = response;
      this.initalizeAutocompleteOptions();
    });
  }

  addItem() {
    this.formService.addItemRow();
    this.initalizeAutocompleteOptions();
  }

  // Method to get the appropriate price for display in autocomplete based on selected currency
  getProductPriceForDisplay(product: any): string {
    const selectedCurrency = this.formService.getCurrencyControl().value;

    // If product has prices for the selected currency
    if (product.prices && product.prices[selectedCurrency] !== undefined) {
      return Number(product.prices[selectedCurrency]).toFixed(2);
    }
    // For RSD, use the default price field
    else if (selectedCurrency === 'RSD' && product.price !== undefined) {
      return Number(product.price).toFixed(2);
    }

    // Default to 0 if no price is found
    return '0.00';
  }
}
