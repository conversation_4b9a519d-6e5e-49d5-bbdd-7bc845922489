<ng-container *transloco="let t">
  <div class="table-container" >
    <div class="bulk-actions" *ngIf="selectedProducts.size > 0">
      <button mat-button color="warn" (click)="deleteSelected()">
        {{ t('delete') }} ({{ selectedProducts.size }})
      </button>
    </div>
    <table mat-table *transloco="let t" [dataSource]="dataSource" matSort multiTemplateDataRows class="products-table" >

      <ng-container matColumnDef="select">
        <th mat-header-cell *matHeaderCellDef>
          <mat-checkbox (change)="toggleAllRows()" [checked]="selection.hasValue() && isAllSelected()"
            [indeterminate]="selection.hasValue() && !isAllSelected()">
          </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let element">
          <mat-checkbox (click)="$event.stopPropagation()" (change)="toggleRow(element)"
            [checked]="selection.isSelected(element)">
          </mat-checkbox>
        </td>
      </ng-container>

      <ng-container matColumnDef="Naziv">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{t('items.name')}} </th>
        <td mat-cell *matCellDef="let element">{{element.name}}</td>
      </ng-container>
      <ng-container matColumnDef="Cena">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{t('price')}}</th>
        <td mat-cell *matCellDef="let element">
          <div>
            {{element.price | rsdCurrency:'RSD ':2:'.':',':3}}
          </div>
          <div *ngIf="element.prices && getAdditionalCurrencies(element).length > 0" class="additional-currencies">
            <span *ngFor="let currency of getAdditionalCurrencies(element)" class="currency-badge">
              {{currency.price | rsdCurrency:currency.symbol:2:'.':',':3}}
            </span>
          </div>
        </td>
      </ng-container>
      <ng-container matColumnDef="gtin">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>GTIN</th>
        <td mat-cell *matCellDef="let element">{{element.gtin}}</td>
      </ng-container>
      <ng-container matColumnDef="productCode">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{t('items.product_code')}}</th>
        <td mat-cell *matCellDef="let element">{{element.productCode}}</td>
      </ng-container>
      <ng-container matColumnDef="taxLabel">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{t('tax_type')}}</th>
        <td mat-cell *matCellDef="let element">
          {{getTaxLabelDisplay(element.taxLabel)}}
        </td>
      </ng-container>
      <ng-container matColumnDef="createdAt">
        <th mat-header-cell *matHeaderCellDef mat-sort-header>{{t('time_created')}}</th>
        <td mat-cell *matCellDef="let element">{{element.createdAt | date:'medium'}}</td>
      </ng-container>
      <ng-container matColumnDef="Akcija">
        <th mat-header-cell *matHeaderCellDef></th>
        <td mat-cell *matCellDef="let element">
          <mat-icon style="margin-right: 15px;"
            (click)="this.actionEmitter.emit({action:'delete',data:element})">delete</mat-icon>
          <mat-icon (click)="this.actionEmitter.emit({action:'edit',data:element})">edit</mat-icon>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayColumns"></tr>
      <tr mat-row *matRowDef="let element; columns: displayColumns;" class="element-row"></tr>
    </table>
  </div>
</ng-container>
