textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    resize: vertical;
}

.container-fluid {
    background: white;
    padding: 40px 30px;
}

.mat-slider {
    width: 100%;
}

button {
    background-color: #008BFF;
    border: none;
    cursor: pointer;
    color: white;
    font-size: 0.9rem;
    align-items: center;
    border-radius: 0.25rem;
    padding: 10px;
    width: 100%;
}

iframe {
    width: 100%;
    min-height: 550px;
}

::ng-deep {
    .mat-slider-thumb-label {
        display: flex !important;
    }

    .mat-slider-horizontal .mat-slider-thumb-label-text {
        transform: rotate(-45deg) !important;
    }
}

.row {
    padding-bottom: 20px;
}

p {
    display: flex;
    margin: 0;
    margin-bottom: 10px;
    align-items: center;
}

.receipt-container {
    width: 100%;
    display: flex;
    justify-content: center;
    overflow-x: auto;
    background-color: #f5f5f5;
    border:#e1e5f2 1px solid;
    padding: 20px 20px !important;
       scroll-padding: 0 20px !important;
}

// Responsive improvements
@media only screen and (max-width: 992px) {
    .container-fluid {
        padding: 30px 20px;
    }

    // Stack receipt preview below controls on tablets
    .col-md-7, .col-md-5 {
        margin-bottom: 20px;
    }

    .col-md-7 {
        padding-right: 15px;
    }
}

@media only screen and (max-width: 768px) {
    .container-fluid {
        padding: 20px 15px;
    }

    // Stack columns on mobile
    .col-md-7, .col-md-5 {
        margin-bottom: 30px;
    }

    .col-md-7 {
        padding-right: 0;
    }

    // Adjust slider layout for mobile
    .col-md-3, .col-md-9 {
        margin-bottom: 10px;
    }

    .col-md-3 {
        text-align: center;
    }

    .col-md-9 {
        justify-content: center;

        p {
            margin: 0 10px;
            font-size: 14px;
        }
    }

    textarea {
        min-height: 100px;
        font-size: 14px;
    }

    iframe {
        min-height: 400px;
    }

    h3 {
        font-size: 20px;
        margin-bottom: 20px;
    }
}

@media only screen and (max-width: 576px) {
  

    // Stack slider components vertically on small mobile
    .col-md-3, .col-md-9 {
        text-align: center;
        margin-bottom: 15px;
    }

    .col-md-9 {
        flex-direction: column;
        align-items: center;

        p {
            margin: 5px 0;
            font-size: 12px;
        }

        .mat-slider {
            margin: 10px 0;
            width: 90%;
        }
    }

    textarea {
        min-height: 80px;
        font-size: 13px;
        padding: 10px;
    }

    iframe {
        min-height: 300px;
    }

    h3 {
        font-size: 18px;
        margin-bottom: 15px;
        text-align: center;
    }

    p {
        font-size: 13px;
        line-height: 1.4;
    }

    // Better button spacing
    .col-md-6 {
        margin-top: 20px;
    }
}